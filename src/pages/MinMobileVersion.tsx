import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { minMobileVersionService } from "@/services/api";
import { MinMobileVersionRequest } from "@/types/api";
import { toast } from "sonner";
import { Smartphone, Loader2, CheckCircle2, AlertTriangle } from "lucide-react";

const MinMobileVersion = () => {
  const queryClient = useQueryClient();
  
  // Form state
  const [formData, setFormData] = useState({
    ios: "",
    android: ""
  });
  
  // Validation state
  const [validationErrors, setValidationErrors] = useState({
    ios: "",
    android: ""
  });

  // Fetch current minimum versions
  const { data: currentVersions, isLoading: isLoadingVersions, error } = useQuery({
    queryKey: ["min-mobile-version"],
    queryFn: minMobileVersionService.getMinMobileVersion
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: (data: MinMobileVersionRequest) => minMobileVersionService.updateMinMobileVersion(data),
    onSuccess: () => {
      toast("Versões mínimas atualizadas com sucesso!", {
        dismissible: true,
      });
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["min-mobile-version"] });
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || "Erro ao atualizar versões mínimas";
      toast(errorMessage, {
        dismissible: true,
      });
    },
  });

  // SEMVER validation regex
  const semverRegex = /^\d+\.\d+\.\d+$/;

  // Validate SEMVER format
  const validateVersion = (version: string, _platform: 'ios' | 'android') => {
    if (!version.trim()) {
      return "Este campo é obrigatório";
    }
    if (!semverRegex.test(version.trim())) {
      return "Formato inválido. Use o formato X.Y.Z (ex: 1.2.3)";
    }
    return "";
  };

  // Handle input change with validation
  const handleInputChange = (platform: 'ios' | 'android', value: string) => {
    setFormData(prev => ({
      ...prev,
      [platform]: value
    }));

    // Validate on change
    const error = validateVersion(value, platform);
    setValidationErrors(prev => ({
      ...prev,
      [platform]: error
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const iosError = validateVersion(formData.ios, 'ios');
    const androidError = validateVersion(formData.android, 'android');

    setValidationErrors({
      ios: iosError,
      android: androidError
    });

    // Check if there are any validation errors
    if (iosError || androidError) {
      toast("Corrija os erros no formulário antes de continuar", {
        dismissible: true,
      });
      return;
    }

    // Submit the form
    updateMutation.mutate({
      ios: formData.ios.trim(),
      android: formData.android.trim()
    });
  };

  // Check if form is valid
  const isFormValid = !validationErrors.ios && !validationErrors.android && 
                     formData.ios.trim() && formData.android.trim();

  // Set initial form data when versions are loaded
  React.useEffect(() => {
    if (currentVersions?.data) {
      setFormData({
        ios: currentVersions.data.ios,
        android: currentVersions.data.android
      });
    }
  }, [currentVersions]);

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center space-y-4">
                <AlertTriangle className="h-12 w-12 text-red-500" />
                <div className="text-center">
                  <h3 className="text-lg font-medium">Erro ao carregar dados</h3>
                  <p className="text-sm text-muted-foreground mt-2">
                    Não foi possível carregar as versões mínimas atuais.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Versão Mínima Mobile</h2>
          <p className="text-muted-foreground">
            Configure as versões mínimas exigidas para os aplicativos iOS e Android
          </p>
        </div>
      </div>

      {/* Current Versions Display */}
      {currentVersions?.data && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Versão Atual iOS
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{currentVersions.data.ios}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Versão Atual Android
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{currentVersions.data.android}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Update Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Smartphone className="mr-2" size={20} />
            Atualizar Versões Mínimas
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingVersions ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Carregando versões atuais...</span>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* iOS Version Input */}
                <div className="space-y-2">
                  <Label htmlFor="ios">Versão Mínima iOS *</Label>
                  <Input
                    id="ios"
                    value={formData.ios}
                    onChange={(e) => handleInputChange('ios', e.target.value)}
                    placeholder="1.0.0"
                    className={validationErrors.ios ? "border-red-500" : ""}
                    disabled={updateMutation.isPending}
                  />
                  {validationErrors.ios && (
                    <p className="text-sm text-red-500">{validationErrors.ios}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Formato: X.Y.Z (exemplo: 1.2.3)
                  </p>
                </div>

                {/* Android Version Input */}
                <div className="space-y-2">
                  <Label htmlFor="android">Versão Mínima Android *</Label>
                  <Input
                    id="android"
                    value={formData.android}
                    onChange={(e) => handleInputChange('android', e.target.value)}
                    placeholder="1.0.0"
                    className={validationErrors.android ? "border-red-500" : ""}
                    disabled={updateMutation.isPending}
                  />
                  {validationErrors.android && (
                    <p className="text-sm text-red-500">{validationErrors.android}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Formato: X.Y.Z (exemplo: 1.2.3)
                  </p>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={updateMutation.isPending || !isFormValid}
                  className="min-w-[120px]"
                >
                  {updateMutation.isPending ? (
                    <>
                      <Loader2 size={16} className="mr-2 animate-spin" />
                      Atualizando...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 size={16} className="mr-2" />
                      ATUALIZAR
                    </>
                  )}
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>

      {/* Help Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Informações Importantes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm text-muted-foreground">
            <p className="mb-2">
              <strong>Formato de Versão:</strong> Use o formato SEMVER (Semantic Versioning) X.Y.Z onde:
            </p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li><strong>X</strong> = Versão principal (major)</li>
              <li><strong>Y</strong> = Versão secundária (minor)</li>
              <li><strong>Z</strong> = Correção (patch)</li>
            </ul>
            <p className="mt-3">
              <strong>Exemplo:</strong> 1.2.3 significa versão principal 1, versão secundária 2, correção 3.
            </p>
            <p className="mt-3">
              <strong>Importante:</strong> Usuários com versões inferiores às configuradas aqui serão obrigados a atualizar o aplicativo.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MinMobileVersion;
