
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Loader2, Eye, Package, User, MapPin, Phone, Mail, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { orderService } from "@/services/api";
import { Order, ListOrdersSuccessResponse, UpdateOrderStatusRequest } from "@/types/api";
import useOrdersRefresh from "@/hooks/useOrdersRefresh";
import { useScreenSize } from "@/hooks/use-mobile";

// Define types for order status
type OrderStatus = "pending" | "processing" | "preparing" | "ready" | "delivering" | "completed" | "cancelled" | "expired";

// Interface for status change dialog
interface StatusChangeDialog {
  isOpen: boolean;
  order: Order | null;
  newStatus: OrderStatus | null;
}

// Interface for order details dialog
interface OrderDetailsDialog {
  isOpen: boolean;
  order: Order | null;
}

// Define allowed status transitions - which statuses can be cancelled
const cancellableStatuses: OrderStatus[] = [
  "pending",
  "processing",
  "preparing",
  "ready"
];

// Define next possible statuses for each current status based on delivery mode
const getNextStatuses = (currentStatus: OrderStatus, deliveryMode: "delivery" | "pickup"): OrderStatus[] => {
  switch (currentStatus) {
    case "pending":
      return ["processing"];
    case "processing":
      return ["preparing"];
    case "preparing":
      // For delivery orders, skip "ready" (aguardando retirada) and go directly to "delivering"
      // For pickup orders, go to "ready" (aguardando retirada)
      return deliveryMode === "delivery" ? ["delivering"] : ["ready"];
    case "ready":
      // Only pickup orders should reach this status
      return deliveryMode === "pickup" ? ["completed"] : [];
    case "delivering":
      // Only delivery orders should reach this status
      return deliveryMode === "delivery" ? ["completed"] : [];
    case "expired":
    case "completed":
    case "cancelled":
      return []; // No status transitions allowed for final states
    default:
      return [];
  }
};

// Custom Badge component with specific colors for order statuses
const StatusBadge: React.FC<{ status: OrderStatus; children: React.ReactNode }> = ({ status, children }) => {
  const getStatusStyles = (status: OrderStatus) => {
    switch (status) {
      case "processing":
        return "bg-[#FF7D29] text-white border-[#FF7D29]"; // Orange
      case "preparing":
        return "bg-[#333446] text-white border-[#333446]"; // Dark gray
      case "ready":
      case "delivering":
        return "bg-[#3674B5] text-white border-[#3674B5]"; // Blue
      case "completed":
        return "bg-[#85B29A] text-white border-[#85B29A]"; // Green
      case "cancelled":
      case "expired":
        return "bg-[#E87575] text-white border-[#E87575]"; // Red
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  return (
    <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ${getStatusStyles(status)}`}>
      {children}
    </span>
  );
};

// Status display names in Portuguese
const statusDisplayNames: Record<OrderStatus, string> = {
  pending: "Pendente",
  processing: "Pedido pendente",
  preparing: "Pedido em separação",
  ready: "Aguardando retirada",
  delivering: "Em rota de entrega",
  completed: "Pedido concluído",
  cancelled: "Pedido cancelado",
  expired: "Expirado"
};

interface CompanyOrdersTabProps {
  companyData?: {
    delivery_modes?: string[];
    external_id?: string; // For filtering orders of a specific company
    partnerCompanyIds?: string[]; // For filtering orders of multiple companies (partner view)
    partnerCompanies?: Array<{ external_id: string; name: string }>; // Company names for partner view
    isAdminView?: boolean; // For admin view - show all orders
    allCompanies?: Array<{ external_id: string; name: string }>; // All companies for admin view
  };
  notificationState?: {
    unreadCount: number;
    lastNotificationTime?: Date;
    connectionState: {
      isConnected: boolean;
      reconnectAttempts: number;
    };
  };
}

const CompanyOrdersTab: React.FC<CompanyOrdersTabProps> = ({ companyData, notificationState }) => {
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);
  const [statusDialog, setStatusDialog] = useState<StatusChangeDialog>({
    isOpen: false,
    order: null,
    newStatus: null
  });
  const [detailsDialog, setDetailsDialog] = useState<OrderDetailsDialog>({
    isOpen: false,
    order: null
  });
  const [reason, setReason] = useState("");

  // Responsive design hooks
  const screenSize = useScreenSize();

  // Determine query key based on context
  const getQueryKey = () => {
    if (companyData?.isAdminView) {
      return ["admin-orders", currentPage];
    } else if (companyData?.partnerCompanyIds) {
      // Partner orders page - use partner-orders key for FCM invalidation
      return ["partner-orders", currentPage];
    } else {
      // Company-specific orders (company details pages)
      return ["orders", currentPage];
    }
  };

  // Fetch orders with React Query
  const { data: ordersResponse, isLoading, error, dataUpdatedAt, isFetching } = useQuery({
    queryKey: getQueryKey(),
    queryFn: () => {
      console.log("🔄 CompanyOrdersTab fetching orders:", {
        queryKey: getQueryKey(),
        currentPage,
        limit: 10,
        apiCall: `orderService.getOrders(${currentPage}, 10)`
      });
      return orderService.getOrders(currentPage, 10);
    },
    select: (response) => response.data as ListOrdersSuccessResponse
  });

  // Initialize orders refresh hook for background notification handling
  const { forceRefreshOrders } = useOrdersRefresh({
    enabled: companyData?.partnerCompanyIds ? true : false, // Only for partner orders
    queryKey: getQueryKey()
  });

  // Debug logging for query state changes
  React.useEffect(() => {
    console.log("🔄 CompanyOrdersTab query state changed:", {
      queryKey: getQueryKey(),
      currentPage,
      isLoading,
      isFetching,
      dataUpdatedAt: new Date(dataUpdatedAt || 0).toLocaleTimeString(),
      ordersCount: ordersResponse?.data?.length || 0,
      totalPages: ordersResponse?.totalPages,
      totalItems: ordersResponse?.totalItems,
      apiResponse: ordersResponse ? {
        limit: ordersResponse.limit,
        pageNumber: ordersResponse.pageNumber,
        totalItems: ordersResponse.totalItems,
        totalPages: ordersResponse.totalPages
      } : null
    });
  }, [currentPage, isLoading, isFetching, dataUpdatedAt, ordersResponse]);

  // Mutation for updating order status
  const updateStatusMutation = useMutation({
    mutationFn: ({ orderId, data }: { orderId: string; data: UpdateOrderStatusRequest }) =>
      orderService.updateOrderStatus(orderId, data),
    onSuccess: () => {
      // Invalidate queries based on context
      if (companyData?.isAdminView) {
        queryClient.invalidateQueries({ queryKey: ["admin-orders"] });
      } else if (companyData?.partnerCompanyIds) {
        queryClient.invalidateQueries({ queryKey: ["partner-orders"] });
      } else {
        queryClient.invalidateQueries({ queryKey: ["orders"] });
      }

      toast("Status do pedido atualizado com sucesso!");
      setStatusDialog({ isOpen: false, order: null, newStatus: null });
      setReason("");
    },
    onError: (error: unknown) => {
      const errorMsg = (error as { response?: { data?: { message?: string } } })?.response?.data?.message || "Erro ao atualizar status do pedido";
      toast(errorMsg);
    }
  });

  // Handle status change
  const handleStatusChange = (order: Order, newStatus: OrderStatus) => {
    // Only show modal for cancellation, proceed directly for other status changes
    if (newStatus === "cancelled") {
      setStatusDialog({
        isOpen: true,
        order,
        newStatus
      });
    } else {
      // Proceed directly with status change for non-cancellation
      const data: UpdateOrderStatusRequest = {
        status: newStatus as Exclude<OrderStatus, "pending" | "expired">,
      };

      updateStatusMutation.mutate({
        orderId: order.order_id,
        data
      });
    }
  };

  // Handle cancel order
  const handleCancelOrder = (order: Order) => {
    if (!cancellableStatuses.includes(order.status)) {
      toast(`Pedidos com status '${statusDisplayNames[order.status]}' não podem ser cancelados`);
      return;
    }
    handleStatusChange(order, "cancelled");
  };

  // Manual refresh function
  const handleManualRefresh = async () => {
    console.log("🔄 Manual refresh triggered");

    try {
      // Use the enhanced refresh function if available (for partner orders)
      if (companyData?.partnerCompanyIds && forceRefreshOrders) {
        await forceRefreshOrders();
      } else {
        // Fallback to standard refresh
        await queryClient.invalidateQueries({
          queryKey: getQueryKey(),
          refetchType: 'active'
        });

        await queryClient.refetchQueries({
          queryKey: getQueryKey()
        });
      }

      toast("Lista de pedidos atualizada!");
      console.log("🔄 Manual refresh completed successfully");
    } catch (error) {
      console.error("🔄 Manual refresh failed:", error);
      toast("Erro ao atualizar lista de pedidos");
    }
  };

  // Confirm status change (only for cancellations now)
  const confirmStatusChange = () => {
    if (
      !statusDialog.order ||
      !statusDialog.newStatus ||
      statusDialog.newStatus !== "cancelled"
    )
      return;

    // For cancellations, reason is mandatory
    if (!reason.trim()) {
      toast("Motivo do cancelamento é obrigatório");
      return;
    }

    const data: UpdateOrderStatusRequest = {
      status: "cancelled",
      reason: reason.trim()
    };

    updateStatusMutation.mutate({
      orderId: statusDialog.order.order_id,
      data
    });

    setStatusDialog({ isOpen: false, order: null, newStatus: null });
    setReason("");
  };

  // Open order details
  const openOrderDetails = (order: Order) => {
    console.log("Opening order details for:", order);
    console.log("Order products:", order.products);
    setDetailsDialog({
      isOpen: true,
      order
    });
  };

  // Format currency
  const formatCurrency = (value: number) => {
    // Values come as integers from backend (centavos), so divide by 100
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value / 100);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR');
  };

  // Calculate total items in order
  const getTotalItems = (products: Order['products']) => {
    return products.reduce((total, product) => total + product.quantity, 0);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Carregando pedidos...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pedidos do Parceiro</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-red-500">
            Erro ao carregar pedidos. Tente novamente.
          </div>
        </CardContent>
      </Card>
    );
  }

  // Filter orders based on company context and exclude pending orders
  const orders = (ordersResponse?.data || []).filter(order => {
    const isNotPending = order.status !== "pending";

    // If filtering for a specific company (company detail page)
    if (companyData?.external_id) {
      return isNotPending && order.company_external_id === companyData.external_id;
    }

    // If filtering for partner companies (partner orders page)
    if (companyData?.partnerCompanyIds && companyData.partnerCompanyIds.length > 0) {
      return isNotPending && companyData.partnerCompanyIds.includes(order.company_external_id);
    }

    // If admin view, show all non-pending orders from all companies
    if (companyData?.isAdminView) {
      return isNotPending;
    }

    // Default: show all non-pending orders
    return isNotPending;
  });
  const totalPages = ordersResponse?.totalPages || 1;

  // Helper function to get company name by external_id
  const getCompanyName = (companyExternalId: string) => {
    // For admin view, use allCompanies
    if (companyData?.allCompanies) {
      const company = companyData.allCompanies.find(c => c.external_id === companyExternalId);
      return company?.name || companyExternalId;
    }
    // For partner view, use partnerCompanies
    if (companyData?.partnerCompanies) {
      const company = companyData.partnerCompanies.find(c => c.external_id === companyExternalId);
      return company?.name || companyExternalId;
    }
    return companyExternalId;
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
              {notificationState && (
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm text-muted-foreground">
                  {notificationState.connectionState.isConnected ? (
                    <span className="flex items-center text-green-600">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                      <span className="hidden sm:inline">Atualizações em tempo real</span>
                      <span className="sm:hidden">Online</span>
                    </span>
                  ) : (
                    <span className="flex items-center text-red-600">
                      <div className="w-2 h-2 bg-red-500 rounded-full mr-2" />
                      Desconectado
                      {notificationState.connectionState.reconnectAttempts > 0 && (
                        <span className="ml-1">
                          (Tentativa {notificationState.connectionState.reconnectAttempts}/5)
                        </span>
                      )}
                    </span>
                  )}
                  {notificationState.lastNotificationTime && (
                    <span className="hidden md:block">
                      Última atualização: {notificationState.lastNotificationTime.toLocaleTimeString()}
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Refresh Button */}
            <Button
              variant="outline"
              size={screenSize === 'mobile' ? 'sm' : 'default'}
              onClick={handleManualRefresh}
              disabled={isFetching}
              className="flex items-center gap-2 w-full sm:w-auto"
            >
              <RefreshCw className={`h-4 w-4 ${isFetching ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">{isFetching ? 'Atualizando...' : 'Atualizar'}</span>
              <span className="sm:hidden">{isFetching ? '...' : 'Atualizar'}</span>
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0 sm:p-6">
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto">
              <Table className="min-w-full">
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[100px] min-w-[100px]">ID</TableHead>
                    <TableHead className="min-w-[150px]">Cliente</TableHead>
                    {(companyData?.partnerCompanyIds || companyData?.isAdminView) && (
                      <TableHead className="min-w-[150px] hidden sm:table-cell">Empresa</TableHead>
                    )}
                    <TableHead className="min-w-[120px] hidden md:table-cell">Data</TableHead>
                    <TableHead className="min-w-[80px] hidden lg:table-cell">Itens</TableHead>
                    <TableHead className="min-w-[100px]">Total</TableHead>
                    <TableHead className="min-w-[120px]">Status</TableHead>
                    <TableHead className="min-w-[100px] hidden md:table-cell">Modalidade</TableHead>
                    <TableHead className="min-w-[120px] hidden lg:table-cell">Finalizado em</TableHead>
                    <TableHead className="w-[120px] min-w-[120px] sticky right-0 bg-background">Ações</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {orders.length > 0 ? (
                  orders.map((order) => (
                    <TableRow key={order.order_id}>
                      <TableCell className="font-medium">{order.order_id}</TableCell>
                      <TableCell>
                        <div className="max-w-[150px] truncate" title={order.user_name}>
                          {order.user_name}
                        </div>
                      </TableCell>
                      {(companyData?.partnerCompanyIds || companyData?.isAdminView) && (
                        <TableCell className="hidden sm:table-cell">
                          <div className="max-w-[150px] truncate" title={getCompanyName(order.company_external_id)}>
                            {getCompanyName(order.company_external_id)}
                          </div>
                        </TableCell>
                      )}
                      <TableCell className="hidden md:table-cell">
                        <div className="text-sm">{formatDate(order.created_at)}</div>
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        <div className="text-sm">{getTotalItems(order.products)}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{formatCurrency(order.amount)}</div>
                      </TableCell>
                      <TableCell>
                        <StatusBadge status={order.status}>
                          <span className="hidden sm:inline">{statusDisplayNames[order.status]}</span>
                          <span className="sm:hidden text-xs">{statusDisplayNames[order.status].substring(0, 8)}...</span>
                        </StatusBadge>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        <span className={`inline-flex items-center rounded-full border px-2 py-1 text-xs font-medium ${
                          order.delivery_mode === "delivery"
                            ? "bg-blue-100 text-blue-800 border-blue-300"
                            : "bg-green-100 text-green-800 border-green-300"
                        }`}>
                          {order.delivery_mode === "delivery" ? "Entrega" : "Retirada"}
                        </span>
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        {order.status === "completed" || order.status === "cancelled" && order.finished_at ? (
                          <span className="text-sm text-green-600 font-medium">
                            {formatDate(order.finished_at)}
                          </span>
                        ) : (
                          <span className="text-sm text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell className="sticky right-0 bg-background">
                        <div className="flex items-center gap-1 sm:gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openOrderDetails(order)}
                            className="h-8 w-8 p-0 sm:h-9 sm:w-auto sm:px-3"
                          >
                            <Eye className="h-4 w-4" />
                            <span className="hidden sm:ml-2 sm:inline">Ver</span>
                          </Button>

                          {getNextStatuses(order.status, order.delivery_mode).length > 0 && (
                            <Select
                              onValueChange={(value) => handleStatusChange(order, value as OrderStatus)}
                            >
                              <SelectTrigger className="w-20 h-8 text-xs sm:w-32 sm:h-9 sm:text-sm">
                                <SelectValue placeholder="..." />
                              </SelectTrigger>
                              <SelectContent>
                                {getNextStatuses(order.status, order.delivery_mode).map((status) => (
                                  <SelectItem key={status} value={status}>
                                    {statusDisplayNames[status]}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}

                          {cancellableStatuses.includes(order.status) && (
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleCancelOrder(order)}
                              className="h-8 w-8 p-0 sm:h-9 sm:w-auto sm:px-3"
                            >
                              <span className="hidden sm:inline">Cancelar</span>
                              <span className="sm:hidden text-xs">X</span>
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={screenSize === 'mobile' ? 4 : screenSize === 'tablet' ? 6 : 10}
                      className="text-center py-8"
                    >
                      <div className="flex flex-col items-center gap-2">
                        <Package className="h-8 w-8 text-gray-400" />
                        <span className="text-gray-500">Nenhum pedido encontrado</span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
              </Table>
            </div>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-4 px-4 sm:px-0">
              <Pagination>
                <PaginationContent className="flex-wrap gap-1">
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => {
                        const newPage = Math.max(1, currentPage - 1);
                        console.log("🔄 Pagination Previous clicked:", { currentPage, newPage });
                        setCurrentPage(newPage);
                      }}
                      className={`cursor-pointer text-xs sm:text-sm ${currentPage === 1 ? "pointer-events-none opacity-50" : ""}`}
                    />
                  </PaginationItem>

                  {/* Show limited pages on mobile */}
                  {screenSize === 'mobile' ? (
                    // Mobile: Show current page and total
                    <PaginationItem>
                      <span className="px-3 py-2 text-sm">
                        {currentPage} de {totalPages}
                      </span>
                    </PaginationItem>
                  ) : (
                    // Desktop/Tablet: Show all pages (with limit)
                    Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                      let page: number;
                      if (totalPages <= 7) {
                        page = i + 1;
                      } else {
                        // Smart pagination for many pages
                        if (currentPage <= 4) {
                          page = i + 1;
                        } else if (currentPage >= totalPages - 3) {
                          page = totalPages - 6 + i;
                        } else {
                          page = currentPage - 3 + i;
                        }
                      }

                      return (
                        <PaginationItem key={page}>
                          <PaginationLink
                            onClick={() => {
                              console.log("🔄 Pagination Link clicked:", { currentPage, targetPage: page });
                              setCurrentPage(page);
                            }}
                            isActive={currentPage === page}
                            className="cursor-pointer text-xs sm:text-sm"
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })
                  )}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => {
                        const newPage = Math.min(totalPages, currentPage + 1);
                        console.log("🔄 Pagination Next clicked:", { currentPage, totalPages, newPage });
                        setCurrentPage(newPage);
                      }}
                      className={`cursor-pointer text-xs sm:text-sm ${currentPage === totalPages ? "pointer-events-none opacity-50" : ""}`}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status Change Dialog */}
      <Dialog open={statusDialog.isOpen} onOpenChange={(open) => {
        if (!open) {
          setStatusDialog({ isOpen: false, order: null, newStatus: null });
          setReason("");
        }
      }}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">Alterar Status do Pedido</DialogTitle>
            <DialogDescription className="text-sm sm:text-base">
              Confirme a alteração do status do pedido{" "}
              <span className="font-medium">{statusDialog.order?.order_id}</span> para{" "}
              <strong>{statusDialog.newStatus && statusDisplayNames[statusDialog.newStatus]}</strong>
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="reason" className="text-sm font-medium">
                {statusDialog.newStatus === "cancelled" ? "Motivo do cancelamento *" : "Observações (opcional)"}
              </Label>
              <Textarea
                id="reason"
                placeholder={
                  statusDialog.newStatus === "cancelled"
                    ? "Informe o motivo do cancelamento..."
                    : "Adicione observações sobre a mudança de status..."
                }
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="mt-2 min-h-[80px] text-sm"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => {
                setStatusDialog({ isOpen: false, order: null, newStatus: null });
                setReason("");
              }}
              className="w-full sm:w-auto order-2 sm:order-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={confirmStatusChange}
              disabled={
                updateStatusMutation.isPending ||
                (statusDialog.newStatus === "cancelled" && !reason.trim())
              }
              className="w-full sm:w-auto order-1 sm:order-2"
            >
              {updateStatusMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span className="hidden sm:inline">Atualizando...</span>
                  <span className="sm:hidden">...</span>
                </>
              ) : (
                "Confirmar"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Order Details Dialog */}
      <Dialog open={detailsDialog.isOpen} onOpenChange={(open) => {
        if (!open) {
          setDetailsDialog({ isOpen: false, order: null });
        }
      }}>
        <DialogContent className="max-w-[95vw] sm:max-w-4xl max-h-[90vh] overflow-y-auto overflow-x-hidden p-4 sm:p-6">
          <DialogHeader className="space-y-2 sm:space-y-3">
            <DialogTitle className="text-lg sm:text-xl">
              Detalhes do Pedido {detailsDialog.order?.order_id}
            </DialogTitle>
            <DialogDescription className="text-sm sm:text-base">
              Informações completas do pedido, incluindo dados do cliente e produtos
            </DialogDescription>
          </DialogHeader>

          {detailsDialog.order && (() => {
            // Debug logging to check order data structure
            console.log('🔍 Order Details Debug:', {
              order_id: detailsDialog.order.order_id,
              status: detailsDialog.order.status,
              reason: detailsDialog.order.reason,
              coupon: detailsDialog.order.coupon,
              discount: detailsDialog.order.discount,
              allFields: Object.keys(detailsDialog.order),
              fullOrder: detailsDialog.order
            });

            // Check for alternative field names that might contain coupon discount
            const possibleCouponFields = Object.keys(detailsDialog.order).filter(key =>
              key.toLowerCase().includes('coupon') || key.toLowerCase().includes('discount')
            );
            console.log('🔍 Possible coupon/discount fields:', possibleCouponFields);

            // Check for alternative reason fields
            const possibleReasonFields = Object.keys(detailsDialog.order).filter(key =>
              key.toLowerCase().includes('reason') || key.toLowerCase().includes('info') || key.toLowerCase().includes('message')
            );
            console.log('🔍 Possible reason fields:', possibleReasonFields);

            return (
            <div className="space-y-4 sm:space-y-6 mt-4 sm:mt-6">
              {/* Order Summary */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold mb-3 flex items-center text-base sm:text-lg">
                    <Package className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                    Informações do Pedido
                  </h4>
                  <div className="space-y-2 sm:space-y-3 text-sm sm:text-base">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                      <span className="font-medium">ID:</span>
                      <span className="font-mono text-blue-600">{detailsDialog.order.order_id}</span>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                      <span className="font-medium">Status:</span>
                      <StatusBadge status={detailsDialog.order.status}>
                        {statusDisplayNames[detailsDialog.order.status] || detailsDialog.order.status}
                      </StatusBadge>
                    </div>
                    {detailsDialog.order.status === "cancelled" && (detailsDialog.order.reason || detailsDialog.order.info) && (
                      <div className="flex flex-col sm:flex-row sm:items-start gap-1 sm:gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <span className="font-medium text-red-700">Motivo do Cancelamento:</span>
                        <span className="text-red-600 break-words">
                          {detailsDialog.order.reason || detailsDialog.order.info || 'Não informado'}
                        </span>
                      </div>
                    )}
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                      <span className="font-medium">Modo de Entrega:</span>
                      <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold w-fit ${
                        detailsDialog.order.delivery_mode === "delivery"
                          ? "bg-blue-100 text-blue-800 border-blue-300"
                          : "bg-green-100 text-green-800 border-green-300"
                      }`}>
                        {detailsDialog.order.delivery_mode === "delivery" ? "Entrega" : "Retirada"}
                      </span>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                      <span className="font-medium">Método de Pagamento:</span>
                      <span>{detailsDialog.order.payment_method || 'N/A'}</span>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                      <span className="font-medium">Criado em:</span>
                      <span className="text-gray-600">{formatDate(detailsDialog.order.created_at)}</span>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                      <span className="font-medium">Atualizado em:</span>
                      <span className="text-gray-600">{formatDate(detailsDialog.order.updated_at)}</span>
                    </div>
                    {detailsDialog.order.status === "completed" || detailsDialog.order.status === "cancelled" && detailsDialog.order.finished_at && (
                      <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                        <span className="font-medium">Finalizado em:</span>
                        <span className="text-green-600 font-medium">
                          {formatDate(detailsDialog.order.finished_at)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-semibold mb-3 flex items-center text-base sm:text-lg">
                    <User className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                    Informações do Cliente
                  </h4>
                  <div className="space-y-2 sm:space-y-3 text-sm sm:text-base">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                      <span className="font-medium">Nome:</span>
                      <span>{detailsDialog.order.user_name}</span>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                      <Mail className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0" />
                      <span className="break-all">{detailsDialog.order.user_email}</span>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                      <Phone className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0" />
                      <span>{detailsDialog.order.user_phone_number}</span>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-start gap-1 sm:gap-2">
                      <MapPin className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0 mt-0.5" />
                      <span className="break-words">{detailsDialog.order.user_address}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Coupon Information - Only show for company coupons */}
              {detailsDialog.order.coupon && (
                <div className="bg-green-50 rounded-lg p-4">
                  <h4 className="font-semibold mb-3 flex items-center text-base sm:text-lg">
                    <svg className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                    Cupom Aplicado
                  </h4>
                  <div className="space-y-2 sm:space-y-3 text-sm sm:text-base">
                    {detailsDialog.order.coupon && (
                      <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                        <span className="font-medium text-green-700">Código:</span>
                        <span className="font-mono bg-green-100 px-2 py-1 rounded text-green-800 w-fit">
                          {detailsDialog.order.coupon}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}



              {/* Financial Summary */}
              <div>
                <h4 className="font-semibold mb-3 flex items-center text-base sm:text-lg">
                  <svg className="h-4 w-4 sm:h-5 sm:w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                  Resumo Financeiro
                </h4>
                <div className="bg-gray-50 p-4 rounded-lg space-y-3 text-sm sm:text-base">
                  {(() => {
                    // Calculate subtotal (sum of all products at original price)
                    const subtotal = detailsDialog.order.products?.reduce((total, product) =>
                      total + ((product.price || 0) * (product.quantity || 0)), 0) || 0;

                    // Calculate total product discounts (product.discount is percentage)
                    const productDiscounts = detailsDialog.order.products?.reduce((total, product) => {
                      const discountAmount = ((product.price || 0) * (product.discount || 0) / 100) * (product.quantity || 0);
                      return total + discountAmount;
                    }, 0) || 0;

                    // Get coupon discount
                    const couponDiscount = detailsDialog.order.discount || 0;

                    // Get shipping fee
                    const shippingFee = detailsDialog.order.shipping_fee || 0;

                    // Final total from API
                    const finalTotal = detailsDialog.order.amount || 0;

                    return (
                      <>
                        <div className="flex justify-between">
                          <span>Subtotal dos Produtos:</span>
                          <span className="font-medium">{formatCurrency(subtotal)}</span>
                        </div>

                        {productDiscounts > 0 && (
                          <div className="flex justify-between text-orange-600">
                            <span>Descontos dos Produtos:</span>
                            <span className="font-medium">-{formatCurrency(productDiscounts)}</span>
                          </div>
                        )}

                        {couponDiscount > 0 && (
                          <div className="flex justify-between text-green-600">
                            <span>Desconto do Cupom:</span>
                            <span className="font-medium">-{formatCurrency(couponDiscount)}</span>
                          </div>
                        )}

                        {shippingFee > 0 && (
                          <div className="flex justify-between text-blue-600">
                            <span>Taxa de Entrega:</span>
                            <span className="font-medium">+{formatCurrency(shippingFee)}</span>
                          </div>
                        )}

                        <hr className="border-gray-300 my-2" />

                        <div className="flex justify-between text-lg font-bold bg-white p-3 rounded border">
                          <span>Total Final:</span>
                          <span className="text-green-600">{formatCurrency(finalTotal)}</span>
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>

              {/* Products */}
              <div>
                <h4 className="font-semibold mb-3 flex items-center text-base sm:text-lg">
                  <Package className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                  Produtos ({detailsDialog.order.products?.length || 0})
                </h4>

                {/* Mobile Card View */}
                {screenSize === 'mobile' && detailsDialog.order.products?.map((product, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 mb-3 shadow-sm">
                    <div className="flex items-start space-x-3">
                      <img
                        src={product.image || "https://placehold.co/60x60?text=No+Image"}
                        alt={product.name || 'Produto'}
                        className="h-12 w-12 rounded object-cover flex-shrink-0"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = "https://placehold.co/60x60?text=No+Image";
                        }}
                      />
                      <div className="flex-1 min-w-0">
                        <h5 className="font-medium text-sm truncate" title={product.name}>
                          {product.name || 'Nome não disponível'}
                        </h5>
                        <div className="mt-2 space-y-1 text-xs text-gray-600">
                          <p><span className="font-medium">Marca:</span> {product.brand || 'N/A'}</p>
                          <p><span className="font-medium">EAN:</span> {product.ean || 'N/A'}</p>
                          <p><span className="font-medium">Quantidade:</span> {product.quantity || 0}</p>
                          <div>
                            <span className="font-medium">Preço Unit.:</span>
                            <div className="inline-block ml-1">
                              {(product.discount || 0) > 0 && (
                                <span className="text-gray-400 line-through text-xs mr-1">
                                  {formatCurrency(product.price || 0)}
                                </span>
                              )}
                              <span>{formatCurrency((product.price || 0) - ((product.price || 0) * (product.discount || 0) / 100))}</span>
                            </div>
                          </div>
                          <p className="font-medium text-green-600">
                            <span>Total Final:</span> {formatCurrency(((product.price || 0) - ((product.price || 0) * (product.discount || 0) / 100)) * (product.quantity || 0))}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Desktop Table View */}
                {screenSize !== 'mobile' && (
                  <div className="rounded-md border overflow-hidden">
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="min-w-[200px]">Produto</TableHead>
                            <TableHead className="min-w-[100px] hidden md:table-cell">Marca</TableHead>
                            <TableHead className="min-w-[120px] hidden lg:table-cell">EAN</TableHead>
                            <TableHead className="min-w-[80px]">Qtd.</TableHead>
                            <TableHead className="min-w-[100px]">Preço Unit.</TableHead>
                            <TableHead className="min-w-[100px]">Total Final</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {detailsDialog.order.products?.map((product, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <div className="flex items-center space-x-3">
                                  <img
                                    src={product.image || "https://placehold.co/40x40?text=No+Image"}
                                    alt={product.name || 'Produto'}
                                    className="h-8 w-8 sm:h-10 sm:w-10 rounded object-cover flex-shrink-0"
                                    onError={(e) => {
                                      (e.target as HTMLImageElement).src = "https://placehold.co/40x40?text=No+Image";
                                    }}
                                  />
                                  <span className="font-medium text-sm sm:text-base truncate" title={product.name}>
                                    {product.name || 'Nome não disponível'}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell className="hidden md:table-cell text-sm">
                                {product.brand || 'N/A'}
                              </TableCell>
                              <TableCell className="hidden lg:table-cell text-sm font-mono">
                                {product.ean || 'N/A'}
                              </TableCell>
                              <TableCell className="text-center font-medium">
                                {product.quantity || 0}
                              </TableCell>
                              <TableCell className="text-sm">
                                <div className="space-y-1">
                                  {(product.discount || 0) > 0 && (
                                    <div className="text-xs text-gray-400 line-through">
                                      {formatCurrency(product.price || 0)}
                                    </div>
                                  )}
                                  <div>{formatCurrency((product.price || 0) - ((product.price || 0) * (product.discount || 0) / 100))}</div>
                                </div>
                              </TableCell>
                              <TableCell className="font-medium text-green-600">
                                <div className="space-y-1">
                                  <div>
                                    {formatCurrency(((product.price || 0) - ((product.price || 0) * (product.discount || 0) / 100)) * (product.quantity || 0))}
                                  </div>
                                </div>
                              </TableCell>
                            </TableRow>
                          )) || (
                            <TableRow>
                              <TableCell colSpan={6} className="text-center py-8">
                                <div className="flex flex-col items-center gap-2">
                                  <Package className="h-8 w-8 text-gray-400" />
                                  <span className="text-gray-500">Nenhum produto encontrado</span>
                                </div>
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                )}
              </div>
            </div>
            );
          })()}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDetailsDialog({ isOpen: false, order: null })}
            >
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CompanyOrdersTab;
